const User = require('../rest/model/audb/User');

const BATCH_SIZE = 1000;

async function patchUsers() {
  const cursor = User.find({}, { _id: 1, em: 1 }).cursor();

  const bulkOps = [];
  let counter = 0;

  for await (const user of cursor) {
    try {
      const decrypted = User.decryptEmail(user.em);
      const encrypted = User.encryptEmail(decrypted);

      bulkOps.push({
        updateOne: {
          filter: { _id: user._id },
          update: { $set: { em: encrypted } }
        }
      });

      counter++;

      if (bulkOps.length === BATCH_SIZE) {
        await User.bulkWrite(bulkOps);
        bulkOps.length = 0;
        console.log(`✅ Updated ${counter}`);
      }
    } catch (e) {
      console.error(`❌ Error processing user ${user._id}`, e);
    }
  }

  if (bulkOps.length > 0) {
    await User.bulkWrite(bulkOps);
    console.log(`✅ Final batch updated. Total: ${counter}`);
  }
}

const run = async () => {
  // const response = await patchUsers();
  // return response;
  const oldEm = User.encryptEmailLegacy('<EMAIL>');
  console.log('oldEm', oldEm);
  const decryptedOldEmail = User.decryptEmail(oldEm);
  console.log('decryptedOldEmail', decryptedOldEmail);
  const newEm = User.encryptEmail(decryptedOldEmail);
  console.log('newEm', newEm);
  const decryptedNewEmail = User.decryptEmail(newEm);
  console.log('decryptedNewEmail', decryptedNewEmail);
};

run().then((result) => {
  console.log(`DONE: ${result}`);
  process.exit(0);
}).catch((err) => {
  console.log(err);
  process.exit(1);
});
