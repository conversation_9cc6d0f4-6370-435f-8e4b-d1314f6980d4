const { HttpError } = require('@s1/api-errors');
const addVodPositionCommand = require('../../commands/user/positions/vod/add');
const getVodPositionCommand = require('../../commands/user/positions/vod/get');
const addSchedulePositionCommand = require('../../commands/user/positions/schedule/add');
const getSchedulePositionCommand = require('../../commands/user/positions/schedule/get');

module.exports = {
  async getVodPosition(req, res) {
    const { params: { id: vod }, user: { id: uid } } = req;

    if (/^\d+_\d+_\d+$/.test(vod)) {
      return res.json({ error: 0, result: { position: 0 } });
    }
    if (!/^\d+$/.test(vod)) throw new HttpError('Wrong vodId', 400);

    const result = await getVodPositionCommand({ uid, vod });
    res.json({ error: 0, result });
  },
  async getSchedulePosition(req, res) {
    const { params: { channel, rdatetime }, user: { id: uid } } = req;
    const result = await getSchedulePositionCommand({ uid, channel, rdatetime });
    res.json({ error: 0, result });
  },
  async addVodPosition(req, res) {
    const { params: { id: vod }, user: { id: uid }, body: { position = 0 } } = req;

    if (/^\d+_\d+_\d+$/.test(vod)) {
      return res.json({ error: 0, result: true });
    }
    if (!/^\d+$/.test(vod)) throw new HttpError('Wrong vodId', 400);

    await addVodPositionCommand({ uid, vod, position: parseInt(position, 10) });
    res.json({ error: 0, result: true });
  },
  async addSchedulePosition(req, res) {
    const { params: { channel, rdatetime }, user: { id: uid }, body: { position = 0 } } = req;
    await addSchedulePositionCommand({ uid, channel, rdatetime, position: parseInt(position, 10) });
    res.json({ error: 0, result: true });
  },
};
