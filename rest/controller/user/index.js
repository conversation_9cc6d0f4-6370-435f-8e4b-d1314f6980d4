const {
  AvatarNotAllowed<PERSON><PERSON>r,
  ActiveKeyNotFoundError,
  IncorrectEmailError,
  EmailSendingError,
  CheckEmailError,
  LoginNeededError,
  UserExpiredError,
  UserDontExistsError,
  UserSuspendedError,
} = require('@s1/api-errors');
const { user: { statuses } } = require('@s1/api-constants');
const { AVATARS } = require('@s1/api-constants').user;
const config = require('@s1/config').get();
const {
  CheckTrialError,
} = require('../../errors/index');
const passport = require('../../setup/passport');
const refundUserCommand = require('../../commands/user/refund');
const refundFraudUserCommand = require('../../commands/user/refundFraud');
const getUserCommand = require('../../commands/user/get');
const getAuthCodeLogin = require('../../commands/user/getAuthCodeLogin');
const getAuthCodePayment = require('../../commands/user/getAuthCodePayment');
const checkAuthCodeStatus = require('../../commands/user/checkAuthCodeStatus');
const checkPaymentStatusFromTime = require('../../commands/user/checkPaymentStatusFromTime');
const addUserPaymentAttemptExtraTime = require('../../commands/user/addUserPaymentAttemptExtraTime');
const _checkUserRestrictions = require('../../commands/user/checkUserRestrictions');
const updateAuthCode = require('../../commands/user/updateAuthCode');
const resetPasswordUserCommand = require('../../commands/user/resetPassword');
const changeNameAndPhoneUserCommand = require('../../commands/user/changeNameAndPhone');
const changeUserAvatarCommand = require('../../commands/user/changeAvatar');
const unfreezeUserCommand = require('../../commands/user/unfreeze');
const adminUnfreezeUserCommand = require('../../commands/user/adminUnfreeze');
const setCarouselUserCommand = require('../../commands/user/setCarousel');
const checkUserCommand = require('../../commands/user/checkUser');
const deleteDevice = require('../../commands/user/registeredDevices/delete');
const _getAppleInviteAppsByAccount = require('../../commands/user/getAppleInviteAppsByAccount');
const _getAppleInviteAccounts = require('../../commands/user/getAppleInviteAccounts');
const _saveAppleInviteAccount = require('../../commands/user/saveAppleInviteAccount');
const _removeAppleInviteAccount = require('../../commands/user/removeAppleInviteAccount');
const _sentAppleInvite = require('../../commands/user/sentAppleInvite');
const _getLastUserIp = require('../../commands/user/getLastUserIp');
const _saveUserIp = require('../../commands/user/saveUserIp');
const _saveDevToolInfo = require('../../commands/user/saveDevToolInfo');
const _getDevToolActions = require('../../commands/user/getDevToolActions');
const _searchUserForAdminPanelPaymentRules = require('../../commands/user/searchUserForAdminPanelPaymentRules');
const _moveUserToPermissionGroups = require('../../commands/user/moveUserToPermissionGroups');
const User = require('../../model/audb/User');
const BlockedEmail = require('../../model/audb/BlockedEmail');
const Session = require('../../model/audb/Session');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const options = require('../../model/audb/Package').populationOptions;
const errorMapper = require('../../errors/maps/express');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

const UserController = {
  async refund(req, res) {
    const { resolved: { paymentLog }, query: { reason } } = req;
    await errorMapper(refundUserCommand({ reason, paymentLog }));
    res.json({ error: 0, result: true, message: 'User refunded and suspended' });
  },
  async refundFraud(req, res) {
    const { resolved: { paymentLog } } = req;
    const locale = getParamFromRequest(req, 'locale');
    await errorMapper(refundFraudUserCommand({ paymentLog, locale }));
    res.json({ error: 0, result: true, message: 'User refunded and blocked' });
  },
  async getUser(req, res) {
    const { user, authenticationError, sessionID, IPInfo, query: { fav, useragent, flash, checkperm, appName } } = req;
    const locale = getParamFromRequest(req, 'locale');
    const result = await getUserCommand({
      user, authenticationError, fav, useragent, flash, sessionID, IPInfo, checkperm, appName, locale,
    });
    res.json(result);
  },
  async checkUser(req, res) {
    const { query: { email } } = req;

    if (!email) return res.send({ error: 0, results: null });

    const emailLower = email.toLowerCase();
    const blocks = await BlockedEmail.getBlocks(emailLower);

    if (blocks && blocks.length > 0) return res.send({ error: 11119, results: blocks });

    const user = await checkUserCommand({ email: emailLower });
    res.send({
      error: 0,
      results: { email: user ? 1 : 0 },
    });
  },
  async setCarousel(req, res) {
    const { user } = req;
    const value = getParamFromRequest(req, 'setto');
    const result = await setCarouselUserCommand({ user, value });
    res.json(result);
  },
  async resetPassword(req, res) {
    const { user } = req;
    const locale = getParamFromRequest(req, 'locale');
    const oldPassword = getParamFromRequest(req, 'oldpassword');
    const newPassword = getParamFromRequest(req, 'newpassword');
    const result = await errorMapper(resetPasswordUserCommand({ user, oldPassword, newPassword, locale }));

    if (result instanceof Error) throw result;

    return res.send({
      errorcode: 0,
      results: req.__('Password changed successfully'),
    });
  },
  async changeNameAndPhone(req, res) {
    const { user: { id } } = req;
    const locale = req.locale;
    const update = {
      name: getParamFromRequest(req, 'name'),
      phonearea: getParamFromRequest(req, 'phonearea'),
      phone: getParamFromRequest(req, 'phone'),
    };
    const user = await changeNameAndPhoneUserCommand({ id, update, locale });
    res.send({
      errorcode: 0,
      result: {
        user,
        msg: 'success',
      },
    });
  },
  /**
   * Modifies the 'working' field in the 'delay' collection via the sessionID, if the 'uid' param wasn't passed.
   * If the secret key is correct, you can change the freeze status using the 'uid'.
   * If the 'starttime' and 'endtime' are exists, the freeze document will delete.
   *
   * @param {string} uid - User ID.
   * @param {string} keyid - Secret key for modifying another users.
   * @param {string} working - Required to change the freeze status from "true" to "false"
   *                           with only the 'endtime' param (without the 'starttime').
   * @param {string} starttime - The unix time to start freezing.
   * @param {string} endtime - The unix time to stop freezing.
   * @returns {Promise<{
   *  [errorcode | error]: number,
   *  [result | results]: string | boolean | [boolean],
   * }>
   */
  async unfreezeUser(req, res) {
    const { user } = req;
    const keyid = getParamFromRequest(req, 'keyid', 0);
    const working = getParamFromRequest(req, 'working');
    const uid = parseInt(getParamFromRequest(req, 'uid', 0));
    const starttime = parseInt(getParamFromRequest(req, 'starttime', 0));
    const endtime = parseInt(getParamFromRequest(req, 'endtime', 0));
    const locale = getParamFromRequest(req, 'locale');

    // TODO move to the commands/userFreeze
    if (keyid === 'oeri3vxmov3wr2iooewer') {
      const userFromAdmin = await User.findOne({ id: uid }).basicPopulation().exec();

      if (!userFromAdmin) {
        throw new CheckEmailError(locale);
      }

      const response = await adminUnfreezeUserCommand({
        user: userFromAdmin,
        starttime,
        endtime,
        working,
        locale,
      });

      return res.send(response);
    }
    // Logic for users who don't know KeyID or if user wants to change yourself freeze status.
    if (!user) {
      throw new LoginNeededError(null, locale);
    }

    const { status } = user;

    if (status === statuses.STATUS_SUSPENDED) {
      throw new UserSuspendedError(locale);
    } else if (status === statuses.STATUS_EXPIRED) {
      throw new UserExpiredError(locale);
    }

    const dismisspassword = getParamFromRequest(req, 'dismisspassword', '');
    const response = await unfreezeUserCommand({
      user,
      password: dismisspassword,
      locale,
    });

    res.send(response);
  },
  /**
   * Authorizes the user by the 'activekey' field.
   * Accepts an 'id' parameter, witch will be set as the new password.
   *
   * @param {string} id - Active key. This field must be equal to activekey in the user's schema.
   * @returns {avoid}
   */
  async activate(req, res, next) {
    const key = getParamFromRequest(req, 'id');
    const locale = getParamFromRequest(req, 'locale');

    if (!key) throw new ActiveKeyNotFoundError(locale);

    const user = await User.findOne({ activekey: key }).populate(options).exec();

    if (!user) throw new ActiveKeyNotFoundError(locale);
    if (req.body) {
      req.body.user = user.email;
      req.body.pass = key.toString();
    }
    if (req.query) {
      req.query.user = user.email;
      req.query.pass = key.toString();
    }

    passport.detectStrategyAndAuthenticate(req, res, () => {
      req.user = user;
      next();
    });
  },
  async checkTrial(req, res) {
    try {
      const { cookiekey, storagekey, flashkey } = req.query;

      if (!cookiekey && !storagekey && !flashkey) res.status(400).send(new CheckTrialError(req.__('Please define at least one query parameter: %s', 'cookiekey, storagekey, fleshkey')));
      else {
        const users = await User.getRelatedUsersUsedTheSameDevice({ cookiekey, storagekey, flashkey });
        res.send(formatResponseIDs(users));
      }
    } catch (error) {
      res.status(400).send(error.message);
    }
  },
  async resendCode(req, res) {
    const email = getParamFromRequest(req, 'email');
    const locale = getParamFromRequest(req, 'locale');
    const incorrectDataError = new IncorrectEmailError(locale);
    const sendEmailError = new EmailSendingError(locale);

    if (!email) throw incorrectDataError;

    const user = await User.findOne().byEmail(email.toLowerCase()).exec();

    if (!user) throw incorrectDataError;

    try {
      if (!user.activekey) {
        const [activeKey] = await Promise.all([User.getActiveKey()]);
        user.activekey = activeKey.toString();
        await user.save();
      }

      const sendingResult = await user.mail('registertemplate', config.email.noReply, null, null, locale);

      if (!sendingResult) throw sendEmailError;

      res.send({ error: 0, success: true });
    } catch (e) {
      throw sendEmailError;
    }
  },

  async logout(req, res) {
    const { body: { id } } = req;

    await Session.drop(req.sessionID);
    res.clearCookie('connect.sid');

    const delResult = await deleteDevice(id);

    res.send({
      errorcode: 0,
      result: { deletedDevice: delResult ? delResult.name : req.__('No device found to delete') },
    });
  },

  getAvatarsList(req, res) {
    const URL_PREFIX = `${config.mainSitePath}/avatars/`;
    const list = AVATARS.map(image => ({
      type: image,
      url: `${URL_PREFIX}${image}`,
    }));
    res.json(list);
  },

  async setAvatar(req, res) {
    const { user: { id } } = req;
    const avatar = getParamFromRequest(req, 'avatar');

    if (!AVATARS.includes(avatar)) throw new AvatarNotAllowedError();

    const user = await changeUserAvatarCommand({ id, update: { avatar } });

    if (!user) throw new UserDontExistsError();

    res.send({
      errorcode: 0,
      error: 0,
      result: {
        user: {
          id: user.id,
          avatar: user.avatar,
        },
      },
    });
  },

  /**
   * This method returns code for login from device
   *
   * @param {string} qrCodeWidth - width for QR Code
   * @param {string} dName - device name
   * @param {string} os - Operation System name
   * @param {string} locale
   * @returns {object} login url, auto login url, code, QR code
   */
  async getAuthCodeLogin(req, res) {
    const locale = getParamFromRequest(req, 'locale', config.i18n.defaultLocale);
    const qrCodeWidth = getParamFromRequest(req, 'qrCodeWidth', 255);
    const dName = getParamFromRequest(req, 'dName');
    const os = getParamFromRequest(req, 'os');
    const appName = getParamFromRequest(req, 'appName');
    const { headers } = req;
    const results = await getAuthCodeLogin({ qrCodeWidth, dName, os, headers, appName, locale });

    res.send({
      errorcode: 0,
      results,
    });
  },
  /**
   * This method returns auto code with QR code for payment/packages pages
   *
   * @param {string} sid
   * @param {string} locale
   * @returns {object} login url, auto login url, code, QR code
   */
  async getAuthCodePayment(req, res) {
    const { user } = req;
    const locale = getParamFromRequest(req, 'locale', config.i18n.defaultLocale);
    const qrCodeWidth = getParamFromRequest(req, 'qrCodeWidth', 255);
    const appName = getParamFromRequest(req, 'appName');
    const results = await getAuthCodePayment(user, 'payment', appName, qrCodeWidth, locale);

    res.send({
      errorcode: 0,
      results,
    });
  },
  /**
   * This method check auth code status logged or expired
   *
   * @param {string} code
   * @param {string} locale
   * @returns {object} {code, type, sid|undefined, dName|undefined, os|undefined} or {expire|not valid code errors}
   */
  async checkAuthCodeStatus(req, res) {
    const locale = getParamFromRequest(req, 'locale', config.i18n.defaultLocale);
    const code = getParamFromRequest(req, 'code');
    const result = await checkAuthCodeStatus(req, code.toLowerCase(), locale);

    res.send(result);
  },
  /**
   * This method update authCode with sid
   * and let device know to able to login with this sid
   *
   * @param {string} code
   * @param {string} sid
   * @param {string} locale
   * @returns {object} login url, auto login url, code, QR code, sid
   */
  async updateAuthCode(req, res) {
    const { body, user, headers } = req;
    const locale = getParamFromRequest(req, 'locale', config.i18n.defaultLocale);
    const result = await updateAuthCode(body, user, headers, locale);

    res.send(result);
  },
  /**
   * This method check last user payment status from some time
   *
   * @param {string} sid
   * @param {number} fromTime
   * @param {string} locale
   * @returns {object} {errorcode:0, paid:true|false}
   */
  async checkPaymentStatusFromTime(req, res) {
    const { user } = req;
    const locale = getParamFromRequest(req, 'locale', config.i18n.defaultLocale);
    const fromTime = getParamFromRequest(req, 'fromTime');
    const result = await checkPaymentStatusFromTime(user, fromTime, locale);

    res.send(result);
  },
  /**
   * This method add extra time for user while trying to pay,
   * because of some payment types comes in few hours later.
   * Used for the billnet payment attempt.
   *
   * @param {string} sid
   * @returns {object} {errorcode:0, success:true|false, message}
   */
  async addPaymentAttemptExtraTime(req, res) {
    const { user } = req;
    const result = await addUserPaymentAttemptExtraTime(user);

    res.send(result);
  },
  /**
   * This method check if user is in the our black list
   *
   * @param {string} sid
   * @param {object} body
   * @returns {object} {error:0, isBlackListed:true|false}
   */
  async checkUserRestrictions(req, res) {
    const { user, body } = req;
    const action = getParamFromRequest(req, 'action');
    const result = await _checkUserRestrictions({ req, res, user, action, body });

    res.send(result);
  },
  async getAppleInviteApps(req, res) {
    const result = await _getAppleInviteAppsByAccount(req.body);

    res.send(result);
  },
  async getAppleInviteAccounts(req, res) {
    const result = await _getAppleInviteAccounts();

    res.send(result);
  },
  async saveAppleInviteAccount(req, res) {
    const { body } = req;
    const result = await _saveAppleInviteAccount(body);

    res.send(result);
  },
  async removeAppleInviteAccount(req, res) {
    const { id } = req.params;
    const result = await _removeAppleInviteAccount(id);

    res.send(result);
  },
  async sentAppleInvite(req, res) {
    const { body } = req;
    const result = await _sentAppleInvite(body);

    res.send(result);
  },
  async getLastUserIp(req, res) {
    const { id: userId } = req.params;
    const result = await _getLastUserIp(userId);

    res.send(result);
  },
  async saveUserIp(req, res) {
    const { user, body } = req;
    const { ip: userIp } = body;
    const result = await _saveUserIp(user, userIp);

    res.send(result);
  },
  async saveDevToolInfo(req, res) {
    const { user, body } = req;
    const result = await _saveDevToolInfo(user, body);

    res.send(result);
  },
  async getDevToolActions(req, res) {
    const { user, body } = req;
    const result = await _getDevToolActions(user, body);

    res.send(result);
  },
  async searchUserForAdminPanelPaymentRules(req, res) {
    const result = await _searchUserForAdminPanelPaymentRules(req.body);

    res.send(result);
  },
  async moveUserToPermissionGroups(req, res) {
    const { body } = req;
    const result = await _moveUserToPermissionGroups(body);

    res.send(result);
  },
};

module.exports = UserController;
