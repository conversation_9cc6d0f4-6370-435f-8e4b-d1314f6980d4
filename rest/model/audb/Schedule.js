const mongoose = require('mongoose');
const moment = require('moment');
const formatPlugin = require('mongoose-model-format');
const format = require('../../service/prepare/old/schedule');
const connection = require('./connection');
const Schedule = require('./class/Schedule');

const Schema = mongoose.Schema;
const scheduleSchema = new Schema(
  {
    _id: Schema.Types.ObjectId,
    channel: { type: Number, required: true },
    time: { type: String, required: true },
    name: { type: String, required: true },
    name_he: { type: String },
    name_en: { type: String },
    description: { type: String },
    description_he: String,
    description_en: String,
    genre: { type: String },
    genre_en: String,
    genre_he: String,
    rdate: { type: Number, required: true },
    rdatetime: { type: Number, required: true },
    lengthtime: { type: Number, required: true },
    wday: { type: Number, required: true },
    weekno: { type: Number, required: false, default: 0 },
    year: { type: Number, default: undefined },
    audioStatus: { type: String },
    translated_at: { type: Number },
    showpic: { type: String },
    showCustomPicForRecords: { type: Boolean },
    ifonline: { type: Number, default: 1 },
    views: { type: Number, default: 0 },
    createdForTtlIndex: { type: Date }, // date for auto remove by ttl index in mongo
  },
  {
    collection: 'schd',
    versionKey: false,
  },
);
scheduleSchema.loadClass(Schedule);
scheduleSchema.plugin(formatPlugin);
scheduleSchema.setFormat(format);
scheduleSchema.virtual('Channel', {
  ref: 'Channel',
  localField: 'channel',
  foreignField: 'id',
  justOne: true,
});
scheduleSchema.query.byDate = function byDate(date) {
  const minTime = moment(date, ['D/M/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'YYYY-M-D']).unix() + 6 * 3600;
  const maxTime = minTime + 24 * 3600;

  return this.where('rdatetime').gte(minTime).lt(maxTime);
};
scheduleSchema.query.byId = function byId(channelId) {
  return this.where('channel').equals(channelId).where('ifshow').equals(0).exec();
};

/**
 * collection: 'schd'
 * @type {Model<Document>}
 */
module.exports = connection.model('Schedule', scheduleSchema);
