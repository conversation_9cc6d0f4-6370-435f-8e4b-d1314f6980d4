const _ = require('lodash');
const {
  user: { SALT_PREFIX, statuses },
} = require('@s1/api-constants');
const { DeviceLimitError, ApiError } = require('@s1/api-errors');
const Bottleneck = require('bottleneck');
const moment = require('moment');
const isLocal = require('is-local-ip');
const i18n = require('../../../helpers/geti18n');
const { Package, populationOptions } = require('../Package');
const BlockIdentity = require('../BlockIdentity');
const Discount = require('../Discount');
const PaymentLog = require('../PaymentLog');
const Invite = require('../Invite');
const Invoice = require('../Invoice');
const Fingerprint = require('../Fingerprint');
const StreamingServer = require('../class/StreamingServer');
const StreamingServerState = require('../StreamingServerState');
const StreamingServerISP = require('../StreamingServerISP');
const StreamingServerCountry = require('../StreamingServerCountry');
const StreamingServerGroup = require('../StreamingServerGroup');
const StreamingServerMap = require('../StreamingServerMap');
const StreamingServerMapAuto = require('../StreamingServerMapAuto');
const StreamingServerLoadBalancer = require('../StreamingServerLoadBalancer');
const StreamingServerISPCountry = require('../StreamingServerISPCountry');
const PackageToFeatures = require('../PackageToFeatures');
const PlayerTemporaryRule = require('../PlayerTemporaryRule');
const PlayerUserRule = require('../PlayerUserRule');
const PlayerGlobalRule = require('../PlayerGlobalRule');
const PlayerDefaultRule = require('../PlayerDefaultRule');
const { MailTemplatePostmark } = require('../MailTemplate');
const UserAdminComment = require('../UserAdminComment');
const RegisteredDevice = require('../RegisteredDevice');
const TemporaryPassword = require('../TemporaryPassword');
const UserGlobal = require('../UserGlobal');
const UserLog = require('../UserLog');
const UserConfig = require('../UserConfig');
const UserLogVod = require('../UserLogVod');
const UserLogLive = require('../UserLogLive');
const UserLogLogin = require('../UserLogLogin');
const UserLogRecord = require('../UserLogRecord');
const UserTrialLog = require('../UserTrialLog');
const Suspended = require('../Suspended');
const Settings = require('../../../service/settings');
const getNextModelId = require('../../../helpers/getNextModelId');
const md5 = require('../../../helpers/md5');
const isSet = require('../../../helpers/isSet');
const generateRandomKey = require('../../../helpers/generateRandomKey');
const getRandomSubSequenceFromString = require('../../../helpers/getRandomSubsequenceFromString');
const generateFakeNames = require('../../../helpers/generateFakeNames');
const generateFakeAddress = require('../../../helpers/generateFakeAddress');
const _getLastBillingAddress = require('../../../helpers/getLastBillingAddress');
const ReferralCode = require('../ReferralCode');
const { LastIdModel } = require('../LastId');
const redis = require('../../../service/redisClient');
const ForgotPassword = require('../ForgotPassword');
const filterPackagesByRules = require('../../../commands/payment/helpers/filterPackagesByRules');
const getDiscountsByPackageId = require('../../../commands/discounts/getByPackageId');
const suspendRelatedUsers = require('../../../commands/suspend/suspendRelatedUsers');
const { OUR_SERVERS_IPS } = require('../../../constants/ip');
const { creationMethods } = require('../../../constants/suspended');
const config = require('../../../../config');
const generateUserNftEmail = require('../../../helpers/generateUserNftEmail');
const removeUserPlayRoutesCache = require('../../../commands/cache/removeUserPlayRoutesCache');
const { getUserLocation } = require('../../../service/maxmind');
const removeManyDevicesCommand = require('../../../commands/user/registeredDevices/deleteMany');
const isBlacklistedUser = require('../../../commands/payment/helpers/rules/isBlacklistedUser');
const isStealerUser = require('../../../commands/payment/helpers/rules/isStealerUser');
const getPaymentBlacklistGeneralConfig = require('../../../commands/paymentBlacklist/getGeneralConfig');
const getStealerGeneralConfig = require('../../../commands/stealer/getGeneralConfig');

const encryptionKey = '#%#SD23!';
const serializeString = (str) => `s:${str.length}:"${str}";`;
const PAID_USERS_COUNT_REDIS_KEY = 'users:paid:count';
const PAID_USERS_COUNT_TTL = 60;
const countLimiter = new Bottleneck({ maxConcurrent: 1 });

class User {
  set error(value) {
    this.__error = value;
  }

  get error() {
    return this.__error;
  }

  set sid(value) {
    this.__sid = value;
  }

  get sid() {
    return this.__sid;
  }

  set email(email) {
    this.em = this.constructor.encryptEmail(email);
  }

  get email() {
    return this.constructor.decryptEmail(this.em);
  }

  set pass(password) {
    this.salt = md5(`${SALT_PREFIX}${Date.now() / 1000}`);
    this.password = md5(`${password}${this.salt}`);
  }

  get isCarousel() {
    return !!parseInt(this.carousel || 0);
  }

  get status() {
    const nowInSeconds = Math.floor(Date.now() / 1000);

    if (this.Freeze) return User.constants.STATUS_FREEZE;
    if (this.suspended) return User.constants.STATUS_SUSPENDED;
    if (!this.expires || parseInt(this.expires) < nowInSeconds) return User.constants.STATUS_EXPIRED;
    if (!this.isactive) return User.constants.STATUS_INACTIVE;

    return User.constants.STATUS_ACTIVE;
  }

  get isStealer() {
    return !!this.Stealer;
  }

  async addBillingAddress(data) {
    const billingAddress = {
      country: data.country,
      state: data.state,
      address: data.address,
      city: data.city,
      zip: data.zip,
    };

    if (data.firstname) billingAddress.firstname = data.firstname;
    if (data.lastname) billingAddress.lastname = data.lastname;

    const billingAddressId = md5(JSON.stringify(billingAddress));

    if (!this.billingAddresses) this.billingAddresses = {};
    if (!this.billingAddresses[billingAddressId]) {
      this.billingAddresses[billingAddressId] = billingAddress;
      // set object property as modified to get it save
      this.markModified('billingAddresses');
      await this.save();
    }
  }

  static async canRegister() {
    if (process.env.NODE_ENV !== 'production') return true;

    const [
      count,
      {
        registration: { threshold },
      },
    ] = await Promise.all([User.getPaidCount(), Settings.get()]);

    return count < threshold;
  }

  /**
   * Decrypt email using legacy mcrypt method (for backward compatibility)
   * @param {string} em - Base64 encoded encrypted email
   * @returns {string} - Decrypted email or empty string if failed
   */
  static decryptEmailLegacy(em) {
    if (!em) return '';

    const MCrypt = require('mcrypt').MCrypt;

    const desEcb = new MCrypt('des', 'ecb');
    desEcb.open(encryptionKey);
    // eslint-disable-next-line no-buffer-constructor
    const plaintext = desEcb.decrypt(Buffer.from(em, 'base64'));
    const serialized = plaintext.toString();
    const regex = /:"(.*?)"/gm;

    return regex.exec(serialized)[1];
  }

  /**
   * Decrypt email using built-in crypto module (requires --openssl-legacy-provider)
   * @param {string} em - Base64 encoded encrypted email
   * @returns {string} - Decrypted email or empty string if failed
   */
  static decryptEmailModern(em) {
    if (!em) return '';

    try {
      const crypto = require('crypto');
      const decipher = crypto.createDecipheriv('des-ecb', encryptionKey, null);
      decipher.setAutoPadding(false);

      let decrypted = decipher.update(em, 'base64', 'binary');
      decrypted += decipher.final('binary');

      // Remove padding manually
      const lastByte = decrypted.charCodeAt(decrypted.length - 1);
      if (lastByte > 0 && lastByte <= 8) {
        // Check if it's valid PKCS padding
        let validPadding = true;
        for (let i = decrypted.length - lastByte; i < decrypted.length; i++) {
          if (decrypted.charCodeAt(i) !== lastByte) {
            validPadding = false;
            break;
          }
        }
        if (validPadding) {
          decrypted = decrypted.substring(0, decrypted.length - lastByte);
        }
      }

      // Parse serialized string format: s:length:"email";
      const regex = /:"(.*?)"/gm;
      const match = regex.exec(decrypted);

      return match ? match[1] : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Decrypt email with hybrid approach: try legacy first, then modern
   * @param {string} em - Base64 encoded encrypted email
   * @returns {string} - Decrypted email
   */
  static decryptEmail(em) {
    if (!em) return '';

    try {
      // First try legacy mcrypt method (for existing encrypted emails)
      const decryptedEmail = this.decryptEmailLegacy(em);

      return decryptedEmail;
    } catch (e) {
      // First try legacy mcrypt method (for existing encrypted emails)
      const decryptedEmail = this.decryptEmailModern(em);

      return decryptedEmail;
    }
  }

  static async decryptEmailWithRedis(em) {
    if (!em) return '';

    const redisKey = `email_${em}`;
    const CACHE_TIME = 30 * 24 * 3600;

    try {
      const cachedResultString = await redis.get(redisKey);

      if (cachedResultString) return cachedResultString;
    } catch (err) {
      console.log(`User::decryptEmailWithRedis() get redis key:${redisKey} error:`, err);
    }

    const email = this.decryptEmail(em);

    try {
      await redis.set(redisKey, email.toString(), 'EX', CACHE_TIME);
    } catch (err) {
      console.log(`User::decryptEmailWithRedis() set redis key:${redisKey} error:`, err);
    }

    return email;
  }

  /**
   * Encrypt email using built-in crypto module (requires --openssl-legacy-provider)
   * @param {string} email - Plain text email
   * @returns {string} - Base64 encoded encrypted email
   */
  static encryptEmail(email) {
    if (!email) return '';

    try {
      const crypto = require('crypto');
      let serialized = serializeString(email.toLowerCase());

      // Apply PKCS padding manually (DES block size is 8 bytes)
      const blockSize = 8;
      const pad = blockSize - (serialized.length % blockSize);
      if (pad < blockSize) {
        serialized += new Array(pad + 1).join(String.fromCharCode(pad));
      }

      const cipher = crypto.createCipheriv('des-ecb', encryptionKey, null);
      cipher.setAutoPadding(false); // We handle padding manually

      let encrypted = cipher.update(serialized, 'binary', 'base64');
      encrypted += cipher.final('base64');

      return encrypted;
    } catch (error) {
      console.error('Error encrypting email:', error);
      return '';
    }
  }

  /**
   * Legacy encrypt method using mcrypt (kept for reference/testing)
   * @param {string} email - Plain text email
   * @returns {string} - Base64 encoded encrypted email
   */
  static encryptEmailLegacy(email) {
    if (!email) return '';

    let serialized = serializeString(email.toLowerCase());
    const MCrypt = require('mcrypt').MCrypt;

    const desEcb = new MCrypt('des', 'ecb');
    const iv = desEcb.generateIv().length;
    const pad = iv - (serialized.length % iv);

    if (pad < iv) serialized += new Array(pad + 1).join(String.fromCharCode(pad));

    desEcb.open(encryptionKey);
    const ciphertext = desEcb.encrypt(serialized);

    return ciphertext.toString('base64');
  }

  static generateCustomEmail(email) {
    const emailParts = email.split('@');
    const emailPrefix = emailParts.length > 1 ? `${emailParts[0]}${emailParts[1].charAt(0)}` : emailParts[0];
    const emailDomain = config.mtpelerin.emailDomains[Math.floor(Math.random() * config.mtpelerin.emailDomains.length)];
    const characters = 'abcdefghijklmnopqrstuvwxyz';
    const charactersLength = characters.length;
    const randomChar = characters.charAt(Math.floor(Math.random() * charactersLength));
    const mtpelerinEmail = `${emailPrefix}${randomChar}@${emailDomain}`;

    return this.encryptEmail(mtpelerinEmail);
  }

  static getEmailName(email) {
    const emailParts = email.split('@');
    const emailName = emailParts[0];

    return emailName;
  }

  static getNftEmailName(email) {
    let emailName = this.getEmailName(email);

    if (emailName.match(/[a-zA-Z]{1}$/)) {
      emailName += getRandomSubSequenceFromString(2, 'abcdefghjkmnprstuvwxyz');
    } else {
      emailName += getRandomSubSequenceFromString(2);
    }

    return emailName;
  }

  async getLastBillingAddress() {
    const lastPaymentAddress = _getLastBillingAddress(this);

    if (lastPaymentAddress) return lastPaymentAddress;

    return null;
  }

  static async getActiveKey() {
    const activeKey = getRandomSubSequenceFromString(6);
    const keyNotUnique = await this.findOne({ activekey: activeKey.toString() });

    if (keyNotUnique) return this.getActiveKey();

    return activeKey;
  }

  static async getPaidCount() {
    const cachedCount = parseInt((await redis.get(PAID_USERS_COUNT_REDIS_KEY)) || 0, 10);

    if (cachedCount) return cachedCount;

    const { count } = (await this.getPaidAggregation().exec())[0] || { count: 0 };
    await redis.set(PAID_USERS_COUNT_REDIS_KEY, count.toString(), 'EX', PAID_USERS_COUNT_TTL);

    return count;
  }

  /**
   * This method checks all user logs (Live, Record, Vod, Login) and returns the last IP address.
   *
   * @param {number} uid - User ID
   * @returns {Promise<string>} IP - ***.**.***.**
   */
  static async getLastIP(uid) {
    const lastUAlog = await this.getLastLog(uid);

    if (lastUAlog.id && lastUAlog.ip && !isLocal(lastUAlog.ip) && !OUR_SERVERS_IPS.has(lastUAlog.ip)) {
      return lastUAlog.ip;
    }

    let lastLoginLog;
    let isValidIp = false;
    let finishedCheck = false;
    let skipCount = 0;
    const chunkSize = 10;

    while (!finishedCheck && !isValidIp) {
      const lastLoginLogs = await UserLogLogin.find({ uid }).sort({ playtime: -1 }).skip(skipCount).limit(chunkSize).exec();

      if (!lastLoginLogs.length) {
        finishedCheck = true;
      } else {
        for (let i = 0; i < lastLoginLogs.length; ++i) {
          const log = lastLoginLogs[i];

          if (log && log.ip && !isLocal(log.ip) && !OUR_SERVERS_IPS.has(log.ip)) {
            isValidIp = true;
            finishedCheck = true;
            lastLoginLog = log;
          }
        }
      }

      skipCount += chunkSize;
    }

    if (lastLoginLog) {
      return lastLoginLog.ip;
    }

    const { registerip } = await this.findOne({ id: uid }).exec();

    return registerip || '';
  }

  /**
   * Checks all logs (Live, Record, Vod) and returns the last log created.
   *
   * @param {number} uid - User ID
   * @returns {Promise<object>} Last action information
   */
  static async getLastLog(uid) {
    const logs = await Promise.all([
      UserLogLive.findOne({ uid }).sort({ playtime: -1 }).exec(),
      UserLogRecord.findOne({ uid }).sort({ playtime: -1 }).exec(),
      UserLogVod.findOne({ uid }).sort({ playtime: -1 }).exec(),
    ]);
    const sortedLogs = logs.filter(Boolean).sort((a, b) => b.playtime - a.playtime);

    if (sortedLogs && sortedLogs.length) {
      return sortedLogs[0];
    }

    return {};
  }

  static getPaidAggregation(useSkipTrial = false, isCount = true) {
    const currentTime = Math.floor(Date.now() / 1000);

    // TODO calculate max package time to filter initial query
    // const maxPackage = await Package.findOne({ length: -1 }).cache(3600).exec();

    // initial filter all payment logs for the last max package days
    const initialMatch = {
      $match: {
        upback: true, // was paid user
        created: { $gte: currentTime - 3600 * 24 * 180 }, // max package has 180 days
      },
    };
    const finalMatch = {
      till: { $gte: currentTime }, // filter not expired user payment package
      suspended: { $ne: true }, // user should not be blocked
    };

    if (useSkipTrial) finalMatch.skipTrialCheck = { $ne: true };

    const plan = [
      initialMatch,
      // join package to the payment log
      {
        $lookup: {
          from: 'package',
          localField: 'package',
          foreignField: 'id',
          as: 'Package',
        },
      },
      {
        $unwind: '$Package',
      },
      // join user config to the payment log
      {
        $lookup: {
          from: 'suspended',
          localField: 'uid',
          foreignField: 'uid',
          as: 'Suspended',
        },
      },
      {
        $unwind: {
          path: '$Suspended',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          till: { $add: ['$created', '$Package.length'] }, // add new 'till' field with user package expire date
          tkey: 1,
          amount: 1,
          uid: 1,
          suspended: { $ifNull: ['$Suspended', false] },
          skipTrialCheck: { $ifNull: ['$Suspended', false] },
        },
      },
      {
        $match: finalMatch,
      },
    ];
    plan.push(
      isCount
        ? {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          }
        : {
            $group: {
              _id: null,
              uids: { $addToSet: '$uid' },
            },
          },
    );

    return PaymentLog.aggregate(plan);
  }

  static async getPaidUserIds(useSkipTrial = false) {
    return (await this.getPaidAggregation(useSkipTrial, false).exec())[0].uids;
  }

  static async getStatistics() {
    const currentTime = Math.floor(Date.now() / 1000);
    const trialPackage = await Package.findOne({ price: 0 }).exec();
    const active = await this.countDocuments({ isactive: 1 }).exec();
    const trial = await this.countDocuments({ package: trialPackage.id, isactive: 1, expires: { $gt: currentTime } }).exec();
    const paid = (await this.getPaidAggregation().exec())[0];
    const paidCount = paid ? paid.count : 0;
    const expired = await this.countDocuments({ isactive: 1, expires: { $lt: currentTime } }).exec();

    return { active, trial, paid: paidCount, expired };
  }

  static async getStreamingServers(ISP, countryCode, stateCode, streamType, config = null, user = null) {
    if (!config && user) config = await UserConfig.findOne({ uid: user.id }).lean();

    let streamingServers = [];
    let singleServer;
    let mainServer;

    const [isBlacklisted, isStealer] = user
      ? await Promise.all([
        isBlacklistedUser({ user: { id: user.id }, checkUserIdOnly: true }),
        isStealerUser({ user: { id: user.id }, checkUserIdOnly: true }),
      ])
      : [false, false];

    if ((isBlacklisted && user && user.skipBlacklistPaymentStreamingServers) && (isStealer && user && user.skipStealerStreamingServers)
      || (!isBlacklisted && !isStealer && config && config.speed)) {
      singleServer = await StreamingServerGroup.getServer(config.speed);

      if (singleServer) {
        mainServer = config.skipemergency ? singleServer : await StreamingServerMap.findWithConditions(singleServer, streamType);
      }
    }
    if (isStealer && user && !user.skipStealerStreamingServers) {
      const stealerGeneralConfig = await getStealerGeneralConfig();
      const stealerStreamingServersIds = stealerGeneralConfig.generalConfig.streamingServers || [];

      if (stealerStreamingServersIds.length) {
        streamingServers = await StreamingServerGroup.decorateServersGetter(stealerStreamingServersIds);
      }
    } else if (isBlacklisted && user && !user.skipBlacklistPaymentStreamingServers) {
      const paymentBlacklistedGeneralConfig = await getPaymentBlacklistGeneralConfig();
      const blacklistedStreamingServersIds = paymentBlacklistedGeneralConfig.generalConfig.streamingServers || [];

      if (blacklistedStreamingServersIds.length) {
        streamingServers = await StreamingServerGroup.decorateServersGetter(blacklistedStreamingServersIds);
      }
    }
    if (streamingServers.length === 0) {
      if (await StreamingServerISPCountry.checkCountryCode(countryCode)) streamingServers = await StreamingServerISP.getStreamingServers(ISP);
      if (countryCode === 'US' && streamingServers.length === 0) streamingServers = await StreamingServerState.getStreamingServers(stateCode);
      if (streamingServers.length === 0) streamingServers = await StreamingServerCountry.getStreamingServers(countryCode);
    }

    streamingServers = streamingServers.filter((server) => !!server);

    // merge main server as the first in the list
    if (mainServer) streamingServers = [mainServer].concat(streamingServers);
    if (!config || !config.skipemergency) {
      const promises = [];
      const keyPairLoadBalancerIdAndStreamingServerId = {};
      const serverSortOrderByIds = [];
      for (const streamingServer of streamingServers) {
        // save servers sorting
        serverSortOrderByIds.push(streamingServer._id);
        promises.push(
          (async () => {
            const server = await StreamingServerMap.findWithConditions(streamingServer, streamType);
            // pair server ID with mapped server
            keyPairLoadBalancerIdAndStreamingServerId[streamingServer._id] = server;
          })(),
        );
      }
      await Promise.all(promises);

      // restore initial sorting
      streamingServers = [];
      serverSortOrderByIds.forEach((servId) => {
        streamingServers.push(keyPairLoadBalancerIdAndStreamingServerId[servId]);
      });

      // filter duplicated servers
      streamingServers = StreamingServer.filterUniqueStreamingServers(streamingServers);
    }
    // disabled auto emergency and user load balancer instead
    if (streamingServers.length) {
      const promises = [];
      const keyPairLoadBalancerIdAndStreamingServerId = {};
      const serverSortOrderByIds = [];
      streamingServers.forEach((streamingServer) => {
        promises.push((async () => {
          // save servers sorting
          serverSortOrderByIds.push(streamingServer._id);
          const server = await StreamingServerMapAuto.getEmergencyAuto(streamingServer);
          // pair server ID with mapped server
          keyPairLoadBalancerIdAndStreamingServerId[streamingServer._id] = server;
        })());
      });
      await Promise.all(promises);

      // restore initial sorting
      streamingServers = [];
      serverSortOrderByIds.forEach((servId) => {
        streamingServers.push(keyPairLoadBalancerIdAndStreamingServerId[servId]);
      });

      // filter duplicated servers
      streamingServers = Array.from(new Set(streamingServers));
    }
    if (streamingServers.length) {
      streamingServers = await StreamingServerLoadBalancer.getLoadBalancerServers(streamingServers, config);
    }
    if (streamingServers.length) {
      // get first server as a main, because it was already merged as the first
      mainServer = _.first(streamingServers);
      streamingServers.splice(0, 1);
    }

    return {
      mainServer,
      secondaryServers: streamingServers,
    };
  }

  getStreamingServers(ISP, countryCode, stateCode, streamType) {
    return this.constructor.getStreamingServers(ISP, countryCode, stateCode, streamType, this.config, this);
  }

  static async fingerprints({ uid, threshold }) {
    const pipeline = [
      { $match: { uid: parseInt(uid), fingerprint: { $nin: ['', 'undefined', null] } } },
      { $group: { _id: { fingerprint: '$fingerprint', ip: '$ip' }, rows: { $sum: 1 } } },
    ];

    return [UserLogLogin, UserLogVod, UserLogLive, UserLogRecord].reduce(async (acc, log) => {
      const allLogs = await acc;
      const logs = await log.aggregate(pipeline).exec();
      const total = logs.reduce((a, b) => a + b.rows, 0);

      return logs.reduce((acc2, { _id: { ip, fingerprint }, rows }) => {
        if ((rows / total) * 100 < threshold) return allLogs;

        return allLogs.fingerprint ? { ...allLogs, [fingerprint]: [...allLogs[fingerprint], ip] } : { ...allLogs, [fingerprint]: [ip] };
      }, acc);
    }, {});
  }

  static foreignPopulateOptions(path = 'users') {
    return {
      path,
      select: 'id em email expires status config Package package isactive',
      populate: this.populateOptions(),
    };
  }

  static async register(data) {
    const salt = md5(`${SALT_PREFIX}${Date.now() / 1000}`);
    const [userPackage, activeKey, userId] = await Promise.all([
      Package.findOne({ price: 0 }).exec(),
      this.getActiveKey(),
      LastIdModel.getNextId(this),
    ]);
    const userData = {
      id: userId,
      em: this.encryptEmail(data.email),
      salt,
      expires: 0,
      name: data.name,
      password: md5(`${data.password}${salt}`),
      registerip: data.ip,
      package: userPackage.id,
      fingerprint: data.fingerprint || false,
      macaddress: data.macaddress || false,
      cookiekey: data.cookiekey || false,
      storagekey: data.storagekey || false,
      flashkey: data.flashkey || false,
      regtime: Date.now() / 1000,
      phone: data.phone,
      phonearea: data.phonearea,
      carousel: 0,
      isactive: 0,
      activekey: activeKey.toString(),
      trezorKey: generateRandomKey(64),
      mtpelerinDeviceUniqueId: generateRandomKey(64),
      mtpelerinEm: this.generateCustomEmail(data.email),
      tazapayEm: this.generateCustomEmail(data.email),
      tazapayName: generateFakeNames(),
      nftAddress: generateFakeAddress(),
      nftEm: generateUserNftEmail(data.email),
      crossmintEm: generateUserNftEmail(data.email),
      permissionGroups: data.permissionGroups || [],
    };
    let newUser;

    try {
      newUser = await this.create(userData);
    } catch (e) {
      userData.id = await LastIdModel.getNextId(this);
      newUser = await this.create(userData);
    }

    if (data.referral) {
      const referral = await ReferralCode.findOne().byCode(data.referral);
      referral.registerInvite(newUser);
    }

    return newUser;
  }

  static populateOptions() {
    const time = Math.floor(Date.now() / 1000);

    return [
      {
        path: 'config',
      },
      {
        path: 'proxyCounts',
      },
      {
        path: 'Freeze',
        match: {
          working: true,
          starttime: { $lte: time },
          endtime: { $gte: time },
        },
      },
      {
        path: 'suspended',
      },
      {
        path: 'Stealer',
      },
      populationOptions,
    ];
  }

  static async blockDevices({ uid, reason }) {
    const fingerprints = await this.fingerprints({ uid, threshold: 10 });
    await BlockIdentity.add({ uid, reason, fingerprints });
    // TODO block device
  }

  static async unblockDevice({ uid }) {
    await BlockIdentity.clear({ uid });
    // TODO unblock device
  }

  async addAdminComment(content, authorId = null) {
    return UserAdminComment.create({
      uid: this.id,
      comment: content,
      deltag: false,
      author_id: authorId,
      created: Math.round(Date.now() / 1000),
    });
  }

  async checkPassword(password) {
    const temporaryPassword = await TemporaryPassword.findOne({ uid: this.id, password }).exec();

    if (temporaryPassword) return true;
    if (this.activekey && password === this.activekey.toString()) return true;
    if (!password || !this.password || !this.salt) return false;

    const md5v = md5(password);
    const md5salt = md5(password + this.salt);

    return md5v === this.password || md5salt === this.password;
  }

  async resetPassword(oldPassword, newPassword) {
    const tempPasswordModel = await TemporaryPassword.findOne({ uid: this.id, password: oldPassword }).exec();

    if (
      md5(`${oldPassword}${this.salt}`) === this.password ||
      md5(oldPassword) === this.password ||
      oldPassword === this.activekey ||
      tempPasswordModel
    ) {
      this.pass = newPassword;
      this.activekey = '';

      await TemporaryPassword.deleteOne({ uid: this.id });
      await removeManyDevicesCommand(this.id);

      return this.save();
    }

    return false;
  }

  async setPassword(password) {
    this.pass = password;

    return this.save();
  }

  async deleteAdminComment(commentId) {
    return UserAdminComment.delete(this.id, commentId);
  }

  async getAdminCommentById(commentId) {
    return UserAdminComment.findOne({ uid: this.id, _id: commentId });
  }

  /**
   * Adds device with default values, is_active = false;
   * @param deviceId
   * @param userAgent
   * @param deviceName
   * @param type
   * @param os
   * @param appName
   * @param userIp
   * @param fingerprint
   * @returns {Promise<*>}
   */
  async addRegisteredDevice({ deviceId, userAgent, deviceName, type, os, appName, userIp, fingerprint = null }) {
    const device = await RegisteredDevice.findOne({ user_id: this.id, deviceId }).exec();

    if (device) return device;

    await removeUserPlayRoutesCache(this.id);
    const formatedDeviceName = deviceName.replace(os, '').trim();

    return RegisteredDevice.create({
      name: formatedDeviceName,
      type,
      user_id: this.id,
      deviceId,
      userAgent,
      os,
      appName,
      userIp,
      is_active: false,
      fingerprint,
    });
  }

  async getRegisteredDevicesList() {
    const registeredDevices = await RegisteredDevice.find({
      user_id: this.id,
      is_active: { $in: [true, 'true'] },
    })
      .sort({ created: -1 })
      .lean();

    return registeredDevices.map((device) => {
      const { countryName, countryCode, cityName } = getUserLocation(device.userIp);
      device.countryName = countryName;
      device.countryCode = countryCode;
      device.cityName = cityName;
      device.countryFlagUrl = {
        svg: `https://${config.baseImagesPath}/static/countries/svg/${countryCode.toLowerCase()}.svg`,
        png: `https://${config.baseImagesPath}/static/countries/png/${countryCode.toLowerCase()}.png`,
      };

      return device;
    });
  }

  /**
   * Checks if user reached active device limit, excludes current device from search and activates it if absent
   * @param {object} deviceInfo - device info include deviceId, os, dName, userAgent, appName, userIp, deviceIsRequire
   * @param {string} locale
   * @returns {Promise<boolean>} true if device limit is not reached or throws error
   */
  async checkDevicesLimit(deviceInfo, locale) {
    const { dName, os, userAgent, deviceId, type, appName, deviceIsRequire, userIp, fingerprint } = deviceInfo;
    const allUserDevices = await RegisteredDevice.find({ user_id: this.id }).exec();
    let numberOfActiveDevices = allUserDevices.filter((device) => device.is_active).length;
    const currentDevice = allUserDevices.filter((device) => device.deviceId === deviceId)[0];
    let newRegisteredDevice = null;

    if (!currentDevice) {
      // While not APPs have implemented devices functionality will create new device only for specific APPs/websites
      if (deviceIsRequire && deviceIsRequire.toString() === '1') {
        if (deviceId && dName && os)
          newRegisteredDevice = await this.addRegisteredDevice({
            deviceId,
            deviceName: dName,
            type,
            appName,
            os,
            userAgent,
            userIp,
            fingerprint,
          });
        else {
          throw new ApiError(400, i18n.__('Device info is required'));
        }
      }
    }
    // for new or inactive(expired) devices need to increase current active devices
    if (currentDevice && !currentDevice.is_active) numberOfActiveDevices += 1;
    if (newRegisteredDevice) numberOfActiveDevices += 1;

    i18n.setLocale(locale);

    if (this.config && this.config.maxconn) {
      if (numberOfActiveDevices > this.config.maxconn) {
        throw new DeviceLimitError(i18n.__('You reached the limit. You can not view on more than %s devices', this.config.maxconn), locale);
      }
    } else {
      const featuresList = await PackageToFeatures.findOne({ id: this.Package.pgid }).cache(3600).exec();

      if (numberOfActiveDevices > featuresList.deviceLimit) {
        throw new DeviceLimitError(i18n.__('You reached the limit. You can not view on more than %s devices', featuresList.deviceLimit), locale);
      }
    }

    const deviceModel = newRegisteredDevice || currentDevice;

    if (deviceModel && !deviceModel.is_active) {
      deviceModel.is_active = true;
    }

    const now = moment().unix();
    const updatePeriodTime = 10 * 60;

    // update model with last play time once in 10 mins
    if (now - deviceModel.updated > updatePeriodTime) {
      deviceModel.updated = now;
    }
    // no need to wait to update current device
    if (deviceModel.isModified()) deviceModel.save();

    return true;
  }

  async unfreeze() {
    if (!this.Freeze) return false;

    this.Freeze.working = false;
    const result = await this.Freeze.save();

    return result;
  }

  async getInvoices({ fromPage = '', sid, coupon, locale }) {
    const userCanPay = moment(this.expires * 1000) < moment().add(2, 'weeks') ? 1 : 0;
    let packageTypes = await PackageToFeatures.find({}).lean().cache(600).exec();
    packageTypes = PackageToFeatures.formatAll(packageTypes, { locale });
    packageTypes = await Promise.all(
      packageTypes.map(async (type) => {
        type.package = await Package.find({ pgid: type.id }).sort({ sor: 1 }).cache(600).exec();

        return type;
      }),
    );
    const tempPackages = [];
    packageTypes.forEach((type) => tempPackages.push(...type.package));
    const allPackages = [];

    await Promise.all(
      packageTypes.map(async (packageType) => {
        let packages = await Promise.all(
          packageType.package.map(async (pack) => {
            delete packageType.package;
            const packageObject = {
              ...pack.format({ nestedId: true, locale }),
              packagegroup: packageType,
              canpay: userCanPay === 1 && pack.enable && pack.price > 0 ? 1 : 0,
              currency: 'usd',
            };

            if (['adminGenerateInvoice', 'adminPanel'].includes(fromPage) && packageObject.canpay === 0) {
              const invoice = await pack.createInvoice(this, pack.id);
              packageObject.tokey = invoice.tkey;

              return packageObject;
            }
            if (packageObject.canpay === 0) return packageObject;
            // do not generate upgrade packages there, we have another endpoint
            if (pack.isUpgrade && !['adminGenerateInvoice', 'adminPanel'].includes(fromPage)) {
              return packageObject;
            }

            const invoice = await pack.createInvoice(this, pack.id);
            packageObject.tokey = invoice.tkey;
            packageObject.plink = await invoice.getUrl(sid, coupon);

            if (fromPage === 'authCode') packageObject.plink += '&w=1';

            const packageDiscountsReponse = await getDiscountsByPackageId(pack.id);
            packageObject.discounts = packageDiscountsReponse.discounts || [];
            packageObject.discounts = packageObject.discounts.map((discount) => Discount.format(discount));

            return packageObject;
          }),
        );

        packages = await filterPackagesByRules({ user: this, packages, fromPage });
        allPackages.push(...packages);
      }),
    );

    return allPackages;
  }

  async getExoPlayerBuffer() {
    const { exominbuffer, exoplaybackbuffer } = this;
    const result = {};

    if (!isSet(exominbuffer) || !isSet(exoplaybackbuffer)) {
      const { exominbuffer, exoplaybackbuffer } = await UserGlobal.getExoPlayerBuffer();

      result.exominbuffer = exominbuffer;
      result.exoplaybackbuffer = exoplaybackbuffer;

      return result;
    }

    result.exoplaybackbuffer = exoplaybackbuffer;
    result.exominbuffer = exominbuffer;

    return result;
  }

  async getUpgradeInvoice(sid, locale) {
    const packageDays = this.Package.days;
    const pack = await Package.findOne({ days: packageDays, isUpgrade: true }).exec();

    const [invoice, packageFeatures] = await Promise.all([
      await this.Package.createInvoice(this, pack.id),
      await PackageToFeatures.findOne({ extra: 1 }).exec(),
    ]);
    const result = packageFeatures ? packageFeatures.format({ locale }) : {};
    const formattedPackage = pack.format({ nestedId: true, locale });
    result.days = packageDays;
    result.price = pack.price;
    result.tokey = invoice.tkey;
    result.plink = await invoice.getUrl(sid);
    result.pricestr = formattedPackage.pricestr;
    const userConfig = await UserConfig.findOne({ uid: this.id }).exec();

    if (userConfig && userConfig.stripeServiceKeyId) {
      result.plink += `&sus=${userConfig.stripeServiceKeyId}`;
    } else {
      const randomServerKey = Invoice.getStripeUSRandomServiceKeyId();
      result.plink += `&sus=${randomServerKey}`;
    }

    result.currency = 'usd';

    return result;
  }

  async getPlayerRule(userAgent, flashVersion) {
    const userId = this.id;
    const temporaryRuleCriteria = {
      uid: userId,
      playerid: { $gt: 0 },
      created: { $gte: Math.round(Date.now() / 1000) - 300 },
    };
    const tempRule = await PlayerTemporaryRule.match(temporaryRuleCriteria);

    if (tempRule) return tempRule;

    const userRuleCriteria = { uid: userId, flash: { $lte: flashVersion } };
    const userRule = await PlayerUserRule.match(userRuleCriteria, userAgent);

    if (userRule) return userRule;

    const globalRuleCriteria = { flash: { $lte: flashVersion } };
    const globalRule = await PlayerGlobalRule.match(globalRuleCriteria, userAgent);

    if (globalRule) return globalRule;

    return PlayerDefaultRule.match({});
  }

  static async mail(tagname, from, to, options = {}, data = {}, locale) {
    const errors = [];

    if (!tagname) errors.push('"tagname" is required');
    if (!from) errors.push('"from" is required');
    if (!to) errors.push('"to" is required');
    if (errors.length) throw new Error(`Send email error: ${errors.join(', ')}`);

    const encryptedEmail = this.encryptEmail(to);
    const currentNgateUser = await this.findOne({ em: encryptedEmail, isNgateUser: 1 }).lean();

    if (currentNgateUser) return false;

    let mailTemplate;

    if (locale && locale !== 'he') mailTemplate = await MailTemplatePostmark.findOne().byTag(`${tagname}_${locale}`).exec();
    if (!mailTemplate) mailTemplate = await MailTemplatePostmark.findOne().byTag(tagname).exec();

    const response = await mailTemplate.send(data, from, to, options);

    return response.Message === 'OK';
  }

  async mail(tagname, from, options = {}, additionalData = {}, locale) {
    const userData = this.toObject();

    if (userData.isNgateUser) return false;

    let mailTemplate;

    if (locale && locale !== 'he') mailTemplate = await MailTemplatePostmark.findOne().byTag(`${tagname}_${locale}`).exec();
    if (!mailTemplate) mailTemplate = await MailTemplatePostmark.findOne().byTag(tagname).exec();

    delete userData.billingAddresses;
    delete userData._id;
    userData.email = this.email;
    const emailData = Object.assign(userData, additionalData);
    const response = await mailTemplate.send(emailData, from, this.email, options);

    return response.Message === 'OK';
  }

  async readAdminComments(pagination) {
    return UserAdminComment.read(this.id, pagination);
  }

  async reduceLogsByComparing({ compareFields, depth, model, query }) {
    const queryCopy = query || {};
    let filterIds = [this.id];
    const ids = new Set();
    const fields = Object.keys(compareFields);
    while (filterIds.length && depth > 0) {
      const records = await model.find({ ...queryCopy, uid: { $in: filterIds } }, compareFields).exec();
      const searchPromises = [];
      const perSetIds = [];
      // eslint-disable-next-line no-loop-func
      fields.forEach((field) => {
        const values = new Set();
        records.forEach((record) => {
          if (!record[field] || record[field] === 'undefined') return;

          values.add(record[field]);
        });
        searchPromises.push(
          model
            .find(
              Object.assign({}, queryCopy, {
                [field]: { $in: [...values] },
                uid: { $nin: [...filterIds, ...ids] },
              }),
              { uid: 1 },
            )
            .exec(),
        );
      });
      const resultSet = await Promise.all(searchPromises);
      resultSet.forEach((results) => {
        const currentSetIds = new Set();
        perSetIds.push(currentSetIds);
        results.forEach(({ uid }) => {
          if (!uid || ids.has(uid)) return;

          currentSetIds.add(uid);
        });
      });
      filterIds = [...perSetIds[0]].filter((id) => perSetIds.reduce((counter, set) => counter + (set.has(id) ? 1 : 0), 0) === perSetIds.length);
      filterIds.forEach(ids.add, ids);
      depth--;
    }

    return [...ids];
  }

  async getReferralCodes() {
    return ReferralCode.find({ fromid: this.id }).sort({ created: -1 }).exec();
  }

  async addReferralCredit() {
    const invite = await Invite.findOne({ inviteid: this.id }).lean().exec();
    const now = Math.floor(Date.now() / 1000);

    if (!invite || invite.added > 0) return false;

    const inviter = await this.constructor.findOne({ id: invite.fromid }).lean().exec();

    if (!inviter) return false;
    if (inviter.extends < now) {
      await UserLog.create({
        uid: inviter.id,
        st: 1007,
        memo: 'add credit failed',
        msg: [
          {
            uid: invite.inviteid,
            msg: 'user expired, can not get credit',
          },
        ],
        otherinfo: invite,
        created: now,
        updated: now,
      });

      return false;
    }

    const TWO_WEEKS = 3600 * 24 * 14;
    await Promise.all([
      this.constructor.updateOne({ id: inviter.id }, { expires: inviter.expires + TWO_WEEKS }),
      Invite.updateOne({ id: invite.id }, { added: TWO_WEEKS, updated: now }),
      UserLog.create({
        uid: inviter.id,
        st: 7,
        memo: 'add credit',
        msg: [
          {
            uid: invite.inviteid,
            msg: 'get 14 days by referral',
          },
        ],
        otherinfo: invite,
        created: now,
        updated: now,
      }),
    ]);

    return true;
  }

  async suspend(reason) {
    if (reason) await this.addAdminComment(reason);

    await Suspended.updateOne({ uid: this.id }, { creationMethod: creationMethods.automatic, description: reason }, { upsert: true });
    await suspendRelatedUsers(parseInt(this.id), null, reason);
  }

  async refund(reason) {
    if (!this.expires) return;

    const { length } = this.Package;
    this.expires -= length;
    await this.save();
    await this.suspend(reason);
  }

  async cancelPackage(packageName, reason) {
    if (!this.expires) return;
    if (reason) await this.addAdminComment(`Cancelled user package ${packageName}, reason: ${reason}`);

    const { length } = this.Package;
    this.expires -= length;
    await this.save();
  }

  /**
   * Method find and return users by cookiekey, storagekey or flashkey
   * It does not check user trial period
   * This is unnecessary method, need to remove in the future
   * Currently used in the main-site
   *
   * @param thone
   * @returns {Promise<{length}|*|*[]>}
   */
  static async getRelatedUsersUsedTheSameDevice(thone) {
    try {
      const findParams = {
        id: {
          $ne: thone.id,
        },
        $or: [],
      };

      for (const key of Object.keys(thone)) {
        if ((key === 'cookiekey' || key === 'storagekey' || key === 'flashkey') && thone[key] && thone[key] !== 'false') {
          const split = thone[key].split('.');
          const items = [];
          split.forEach((item) => {
            items.push(new RegExp(`${item}`));
          });
          findParams.$or.push({ [key]: { $in: items } });
        }
      }

      if (!findParams.$or.length) return [];

      const users = await this.find(findParams)
        .lean()
        .exec()
        .then((result) => result.filter((user) => !!user.id !== thone.id));

      if (!users.length) return [];

      const uids = [];
      users.forEach((user) => {
        uids.push(user.id);
      });
      const ufs = await Fingerprint.find({ userid: { $in: uids } });
      await Promise.all(
        users.map(async (user) => {
          user.email = await this.decryptEmailWithRedis(user.em);
          user.userfinger = null;

          if (ufs.length)
            ufs.forEach((uf) => {
              if (user.id === uf.userid) user.userfinger = uf;
            });

          return user;
        }),
      );

      return users;
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async addTrialPeriod(ip) {
    const now = Math.floor(Date.now() / 1000);
    const previousTrialsData = [];
    const logMessages = [];
    const fingerprintByIp = await Fingerprint.findOne({
      ipaddress: ip,
      fingerprint: this.fingerprint,
      created: {
        $gte: moment().subtract(90, 'days').unix(),
      },
    }).exec();

    if (fingerprintByIp) {
      previousTrialsData.push(fingerprintByIp);
      logMessages.push({
        uid: fingerprintByIp.userid,
        msg: `already get trial by fingerprint ${fingerprintByIp.fingerprint} and ip ${ip}`,
      });
    }

    await Promise.all(
      ['cookiekey', 'storagekey', 'flaskkey'].map(async (key) => {
        const fingerprintsByKey = await Fingerprint.getFingerprintsBy(key, this[key], this.id);
        fingerprintsByKey.forEach((fingerprint) => {
          previousTrialsData.push(fingerprint);
          logMessages.push({
            uid: fingerprint.userid,
            msg: `already get trial by ${key} ${fingerprint[key]}`,
          });
        });
      }),
    );

    if (previousTrialsData.length > 0) {
      this.isactive = 1;
      await Promise.all([
        UserLog.create({
          uid: this.id,
          st: 0,
          memo: 'already get trial',
          msg: logMessages,
          otherinfo: previousTrialsData,
          created: now,
          updated: now,
        }),
        this.save(),
      ]);

      return;
    }

    const trialsFromCurrentIp = await UserTrialLog.find({
      ip: this.registerip,
      regtime: { $gte: moment().subtract(30, 'days').unix() },
    })
      .lean()
      .exec();

    if (trialsFromCurrentIp && trialsFromCurrentIp.length >= 2) {
      this.isactive = 1;
      this.activekey = '';
      await this.save();
      await Promise.all(
        trialsFromCurrentIp.map(async (trialsFromCurrentIp) => {
          const users = await this.constructor.find({ em: trialsFromCurrentIp.em }, { id: 1, em: 1 }).lean().exec();
          users.forEach((user) => {
            previousTrialsData.push(user);
            logMessages.push({
              uid: user.id,
              msg: `user got a trial before with this ip with that users ${this.constructor.decryptEmail(user.em)}`,
            });
          });
        }),
      );
      await UserLog.create({
        uid: this.id,
        st: 0,
        memo: 'already get trial',
        msg: logMessages,
        otherinfo: previousTrialsData,
        created: now,
        updated: now,
      });

      return;
    }

    const trialPackage = await Package.findOne({ price: 0 }).exec();
    const expires = (now > this.expires ? now : this.expires) + trialPackage.length;
    const fingerprintId = await getNextModelId(Fingerprint);
    const trialLogId = await getNextModelId(UserTrialLog);
    await Fingerprint.create({
      id: fingerprintId,
      ipaddress: ip,
      fingerprint: this.fingerprint,
      cookiekey: this.cookiekey,
      userid: this.id,
      fromregpcheckandaddtrial: 1,
      expires,
      serverss: {},
    });
    this.expires = expires;
    this.isactive = 1;
    this.activeby = 'user';
    this.package = trialPackage.id;
    this.activekey = '';
    await Promise.all([
      UserTrialLog.create({
        id: trialLogId,
        ip: this.registerip,
        em: this.em,
        regtime: now,
      }),
      this.save(),
      this.constructor.deleteMany({ id: { $ne: this.id }, em: this.em }),
    ]);
  }

  /**
   *  This method creates an activekey.
   *  The active key is the new password by which you can enter your account.
   */
  async forgotPassword() {
    this.activekey = await this.constructor.getActiveKey();
    this.activekeygentime = moment().unix();
    const nextForgotId = await getNextModelId(ForgotPassword);
    await Promise.all([
      ForgotPassword.create({
        id: nextForgotId,
        uid: this.id,
        created: moment().unix(),
      }),
      this.save(),
      removeManyDevicesCommand(this.id),
    ]);
  }

  async clearActiveKey() {
    this.activekey = '';
    await this.save();
  }
}

User.constants = statuses;
User.getPaidCount = countLimiter.wrap(User.getPaidCount.bind(User));

module.exports = User;
