const Notice = require('../../model/audb/Notice');
const getUnixTimestampRoundedByMinutes = require('../../helpers/getUnixTimestampRoundedByMinutes');

/**
 * Returns all active notices that should end after the current time expires.
 *
 * @param locale - Current locale
 * @returns {Promise<[{}]>} - All notices
 */
module.exports = async (locale) => {
  const now = getUnixTimestampRoundedByMinutes(10) - 5 * 60;

  const notices = await Notice.find({
    starttime: { $lte: now },
    endtime: { $gte: now },
  }).cache(300).exec()
    .then(notices => Promise.all(notices.map(notice => notice.format({ locale }))));
  const filteredNotices = notices.filter(notice => !notice.tagname || notice.tagname && !notice.tagname.toLowerCase().includes('suggest_app_'))

  return filteredNotices;
};
