/* eslint-disable no-multi-assign */
const moment = require('moment');
const User = require('../../model/audb/User');
const Channel = require('../../model/audb/Channel');
const Schedule = require('../../model/audb/Schedule');
const ScheduleOverride = require('../../model/audb/ScheduleOverride');
const ChannelClass = require('../../model/audb/class/Channel');
const ScheduleClass = require('../../model/audb/class/Schedule');
const { IncorrectGetScheduleOptions } = require('../../errors');
const i18n = require('../../helpers/geti18n');
const sendTelegramMessage = require('../../helpers/sendTelegramMessage');
const config = require('../../../config');
const schedulesCache = require('../../service/cache/schedules');

const DATE_FORMATS = ['D/M/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'YYYY-M-D'];
const CACHE_CHANNELS_TTL = 60 * 60;
const CACHE_TTL = 55;
const defaultStars = {
  star: 3,
  startotal: 1,
  isAlreadyRated: false,
};

/**
 * Schedules day calculate from 6:00 to 6:00, not from 00:00.
 * It's mean that one query like that this query addicted to timestamp
 * /schbydate.php?cid=${cid}&date=${yesterday}&record=0 has different logic at sending on 5:00 and 7:00
 * In 7:00 we don't care about record query param but at 5:00 we should sent them
 *
 * @param isAll - used only in schbydateall.php route. Work only with gid param and add channel info to response
 * @param channelId - from cid param. Search schedules by this channel
 * @param groupId - from gid param. Search schedules by this channel group, response contains schedules of many channels
 * @param user
 * @param rawDate - from date param. By default use today date.
 * If you set tomorrow date you will get all schedules from current timestamp and all tomorrow schedules...
 * @param record {number} - Can be 0 or 1, show only past records, which finished before the current hour and 00 minutes
 * So if you set records to 1 and set future date you will get empty result.
 * If you set date from past - this param does not used.
 * @param recordWithDate - from recordwithdate query param, like record but has more priority.
 * If you set this param to 1 - never mind if date was from past. And add to response name of 14 days from past to today
 * like that:
 * { he: 'שני', en: 'Monday', date: '23/08/2021' },
 * { he: 'ראשון', en: 'Sunday', date: '22/08/2021' }
 * Response will have another structure {scheds: ..., recorddate: ...}
 * @param pageSize
 * @param isPageUndefined
 * @param skip
 * @param simplify
 * @param showId - display mongoID or not, values '1' or '0'
 * @param fewChannels - param shows if we have several channels in group (gid query param)
 * @param showDescription - from withdescription query param. Return schedules with or without description.
 * It's NOT filter option
 * @param locale
 * @param ISP
 * @param countryCode
 * @param stateCode
 * @returns {Promise<[]|*|{recorddate: (*|*), scheds: ([]|*)}|*[]>}
 */
module.exports = async ({
  isAll,
  channelId,
  groupId,
  user,
  rawDate,
  record,
  recordWithDate,
  pageSize,
  isPageUndefined,
  skip,
  simplify,
  showId,
  fewChannels,
  showDescription,
  isLivePage,
  locale,
  ISP,
  countryCode,
  stateCode,
}) => {
  let startTime;
  let timeLog;

  if (config.isSlowLog) {
    startTime = Date.now();
    timeLog = {
      api: 'schedules',
      request: {
        isAll,
        channelId,
        groupId,
        userId: user ? user.id : null,
        rawDate,
        record,
        recordWithDate,
        pageSize,
        isPageUndefined,
        skip,
        simplify,
        showId,
        fewChannels,
        showDescription,
        locale,
        ISP,
        countryCode,
        stateCode,
      },
      startTime: moment().format('YYYY/MM/DD, h:mm:ss.SSS'),
    };
  }

  const todayMoment = moment().subtract(6, 'hours');
  const today = todayMoment.format('YYYY-MM-DD');
  let isDateFromPast = !rawDate ? false : moment(rawDate, DATE_FORMATS).unix() < moment(today, 'YYYY-MM-DD').unix();
  const isTomorrow = moment(rawDate, DATE_FORMATS).startOf('day').unix() === moment(todayMoment).add(1, 'days').startOf('day').unix();
  const isRecordRequested = recordWithDate || isDateFromPast ? true : !!parseInt(record);
  const isRecordUndefined = !isRecordRequested && typeof record === 'undefined' && isPageUndefined;

  if (isRecordRequested && !isDateFromPast) isDateFromPast = true;

  let date = rawDate && ((isRecordRequested && isDateFromPast) || (!isRecordRequested && !isDateFromPast)) ? rawDate : today;
  i18n.setLocale(locale);

  if (isTomorrow && !isRecordRequested && !isRecordUndefined) date = today;
  if (!channelId && !groupId) throw new IncorrectGetScheduleOptions(i18n.__('No channel or group ID provided'));
  if (config.isSlowLog) timeLog.initConsts = Date.now() - startTime;

  const streamingServers = await User.getStreamingServers(ISP, countryCode, stateCode, 2, user ? user.config : {}, user);
  const thumbnailOptions = {
    domain: streamingServers.mainServer.sip,
  };

  const cacheKey = `schedules_${channelId}_${rawDate}_${isAll}_${groupId}_${record}_${recordWithDate}_${pageSize}_${isPageUndefined}_${skip}_${simplify}_${showId}_${fewChannels}_${showDescription}_${isLivePage}_${locale}_${thumbnailOptions.domain}`;

  const { success, result, channelMap, channelIds } = await schedulesCache.wrap(
    cacheKey,
    '10m',
    async () => {
      const channelsQuery = groupId ? Channel.find({ ifshow: 0 }).byGroup(groupId) : Channel.find({ ifshow: 0 }).byId(channelId);
      const channels = await channelsQuery
        .cache(CACHE_CHANNELS_TTL)
        .exec()
        .then((channels) => Promise.all(channels.map(async (channel) => channel.format({ locale }))));

      if (config.isSlowLog) timeLog.loadChannels = Date.now() - startTime;
      if (!channels.length)
        return {
          success: false,
          result: [],
          channelMap: [],
          channelIds: [],
        };

      const channelIds = [];
      const channelMap = channels.reduce((map, channel) => {
        channelIds.push(channel.id);
        map[channel.id] = channel;
        channel.getRecordThumbnailUrl = ChannelClass.prototype.getRecordThumbnailUrl.bind(channel);
        channel._id = { $id: channel._id };

        return map;
      }, {});

      if (config.isSlowLog) timeLog.getRecordThumbnailUrls = Date.now() - startTime;

      const schedules = [];
      const [tempSchedules, schedulesOverride] = await Promise.all([
        Schedule.find().where('channel').in(channelIds).byDate(date).sort({ channel: 1, rdatetime: 1 }).cache(CACHE_TTL).exec(),
        ScheduleOverride.find().where('channel').in(channelIds).byDate(date).sort({ channel: 1, rdatetime: 1 }).cache(CACHE_TTL).exec(),
      ]);

      if (config.isSlowLog) timeLog.loadSchedulesAndOverrides = Date.now() - startTime;

      const existsSchedules = new Map();
      for (const schedule of tempSchedules) {
        const key = `${schedule.channel}_${schedule.rdatetime}`;

        if (!existsSchedules.has(key)) {
          existsSchedules.set(key, true);
          schedules.push(schedule);
        }
      }

      const existsSchedulesOverride = new Map();
      for (const scheduleOverride of schedulesOverride) {
        const key = `${scheduleOverride.channel}_${scheduleOverride.rdatetime}`;
        existsSchedulesOverride.set(key, scheduleOverride);
      }

      const skipped = {};

      if (config.isSlowLog) timeLog.getStreamingServers = Date.now() - startTime;

      const result = schedules.reduce((accumulator, schedule) => {
        const channelId = schedule.channel;
        const scheduleChannel = (schedule.Channel = channelMap[channelId]);
        const channelRoot = (accumulator[channelId] =
          accumulator[channelId] ||
          (isAll
            ? {
              sch: [],
              info: scheduleChannel,
            }
            : []));
        const channelAccumulator = isAll ? channelRoot.sch : channelRoot;

        const isRecordedSchedule = ScheduleClass.prototype.isRecorded.call(schedule);
        const isFinishedSchedule = ScheduleClass.prototype.isFinished.call(schedule);
        const skipCurrentSchedule = (isRecordRequested && !isRecordedSchedule) || (!isRecordRequested && isFinishedSchedule);

        if (!isRecordUndefined && skipCurrentSchedule) return accumulator;
        if (channelAccumulator.length === pageSize) return accumulator;
        if (skip > 0) {
          skipped[channelId] = skipped[channelId] || 0;

          if (skipped[channelId] !== skip) {
            skipped[channelId]++;

            return accumulator;
          }
        }

        const scheduleItem = Schedule.format(schedule, {
          scheduleChannel,
          locale,
          showDescription,
          simplify,
          thumbnailOptions,
        });

        // format picture url time the same as for the play url
        const { recordbefore = 2, recordbeforesecond = 0 } = scheduleChannel;
        const beforeTime = recordbefore * 60 + recordbeforesecond;
        const _tempSchedule = schedule.toObject();
        _tempSchedule.rdatetime -= beforeTime;

        // check if need to override picture
        if (scheduleItem.isradio) {
          scheduleItem.showpic = null;
        } else if (!isLivePage) {
          const overrideKey = `${schedule.channel}_${schedule.rdatetime}`;
          const existsScheduleOverride = existsSchedulesOverride.get(overrideKey);

          if (existsScheduleOverride) {
            scheduleItem.showpic = existsScheduleOverride.getThumbnailUrl();
          } else {
            scheduleItem.showpic = schedule.getThumbnailUrl(scheduleChannel, thumbnailOptions);
          }
        }

        scheduleItem.position = 0;
        scheduleItem.isAlreadySeen = false;
        scheduleItem.isRecorded = isRecordedSchedule;
        Object.assign(scheduleItem, defaultStars);

        if (showId === '1') scheduleItem._id = schedule._id;

        channelAccumulator.push(scheduleItem);

        return accumulator;
      }, {});

      return { success: true, result, channelMap, channelIds };
    },
  );

  if (config.isSlowLog) timeLog.loadSchedulesTotal = Date.now() - startTime;
  if (!success) return [];

  channelIds.forEach((channelId) => {
    if (result[channelId]) return;

    const channelInfo = channelMap[channelId];
    const schedulesOptions = {
      channelInfo: [channelInfo],
      date,
      isRecordRequested,
      thumbnailDomain: thumbnailOptions.domain,
      locale,
    };
    result[channelId] = isAll
      ? {
        sch: Schedule.getDefaultSchedules(schedulesOptions),
        info: channelInfo,
      }
      : Schedule.getDefaultSchedules(schedulesOptions);
  });

  if (config.isSlowLog) timeLog.channelIdsforEach = Date.now() - startTime;
  if (recordWithDate) {
    const weekdays =
      channelIds.length === 1
        ? await ChannelClass.prototype.getWeekdays.call(channelMap[channelIds[0]], thumbnailOptions.domain)
        : Channel.getWeekdays();

    if (config.isSlowLog) timeLog.recordWithDate = Date.now() - startTime;
    if (config.isSlowLog && Date.now() - startTime > config.slowLogTimeout) sendTelegramMessage(timeLog);

    return {
      scheds: channelIds.length === 1 && !fewChannels ? (isAll ? result[channelIds[0]].sch : result[channelIds[0]]) : result,
      recorddate: weekdays,
    };
  }
  if (config.isSlowLog && Date.now() - startTime > config.slowLogTimeout) sendTelegramMessage(timeLog);

  return channelIds.length === 1 && !fewChannels ? (isAll ? result[channelIds[0]].sch : result[channelIds[0]]) : result;
};
