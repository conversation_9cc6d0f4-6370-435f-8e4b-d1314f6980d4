const moment = require('moment-timezone');
const log = require('@s1/log').create(__filename);
const i18n = require('../../helpers/geti18n');
const Channel = require('../../model/audb/Channel');
const Schedule = require('../../model/audb/Schedule');
const ScheduleClass = require('../../model/audb/class/Schedule');
const apiCache = require('../../service/cache');
const { ChannelNotFound, WrongEPGs, CannotSaveSchedule } = require('../../errors');

const DATE_FORMATS = ['M/D/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'YYYY-M-D'];

const prepareTimes = (date) => {
  const currentMoment = moment(date, DATE_FORMATS).tz('Asia/Jerusalem').startOf('day');
  const currentDateString = currentMoment.format('YYYY-MM-DD');
  const currentUnixTime = currentMoment.unix();
  const minTime = currentMoment.add(6, 'hours').unix();
  const tomorrowDateString = currentMoment.add(1, 'day').startOf('day').format('YYYY-MM-DD');
  const tomorrowUnixTime = currentMoment.unix();
  const maxTime = currentMoment.add(6, 'hours').unix();

  return { currentUnixTime, tomorrowUnixTime, tomorrowDateString, currentDateString, minTime, maxTime };
};

const convertEpgToSchedules = (
  epgs,
  indexedOldSchedules,
  channel,
  updateMode,
  tomorrowDateString,
  currentDateString,
  tomorrowUnixTime,
  currentUnixTime,
) => {
  const schedules = [];

  epgs.forEach((epg) => {
    const isFromTomorrow = moment.duration(epg.time.trim(' ')).asSeconds() < 6 * 3600;
    const recordTimeString = `${isFromTomorrow ? tomorrowDateString : currentDateString} ${epg.time}`;
    // Jerusalem and Kiev are in the same timezone,
    // but Jerusalem changed summer time on 26th of March and other world in 28th of March
    // need to do correlation on time difference
    const summerTimeCorrectionHours = 0;
    // Disabled because of rdatetime in few days are increased by 1h by some mistake
    // const summerTimeCorrectionHours =
    //   (moment(recordTimeString, 'YYYY-MM-DD HH:mm').tz('Asia/Jerusalem').utcOffset() -
    //     moment(recordTimeString, 'YYYY-MM-DD HH:mm').tz('Europe/Kiev').utcOffset()) /
    //   60;
    const rdatetime = moment(recordTimeString, 'YYYY-MM-DD HH:mm').add(summerTimeCorrectionHours, 'hours').unix();
    let schedule = updateMode ? indexedOldSchedules[rdatetime] : {};
    let isNewSchedule = false;

    if (!schedule) {
      updateMode = false;
      schedule = {};
      isNewSchedule = true;
    }

    let isNameChanged = false;
    let isDescriptionChanged = false;
    let isGenreChanged = false;

    schedule.channel = channel.id;

    if (epg.name && epg.name.trim() && schedule.name !== epg.name) {
      schedule.name = epg.name;
      isNameChanged = true;
    }
    if (epg.description && epg.description.trim() && schedule.description !== epg.description) {
      schedule.description = epg.description;
      isDescriptionChanged = true;
    }
    if (epg.genre && epg.genre.trim() && schedule.genre !== epg.genre) {
      schedule.genre = epg.genre;
      isGenreChanged = true;
    }

    // set only hebrew fields and english name if exist
    // the other set empty, they will be translate later
    i18n.getLocales().forEach((locale) => {
      if (locale === 'he') return;
      if (isNameChanged) schedule[`name_${locale}`] = locale === 'en' && epg.name_en && epg.name_en.trim() !== '' ? epg.name_en : '';
      if (isDescriptionChanged)
        schedule[`description_${locale}`] = locale === 'en' && epg.description_en && epg.description_en.trim() !== '' ? epg.description_en : '';
      if (isGenreChanged) schedule[`genre_${locale}`] = locale === 'en' && epg.genre_en && epg.genre_en.trim() !== '' ? epg.genre_en : '';
    });

    if (schedule.rdatetime !== rdatetime) schedule.rdatetime = rdatetime;
    if (schedule.rdate !== isFromTomorrow ? tomorrowUnixTime : currentUnixTime) schedule.rdate = isFromTomorrow ? tomorrowUnixTime : currentUnixTime;
    if (schedule.time !== epg.time) schedule.time = epg.time;
    if (schedule.wday !== moment(currentDateString, DATE_FORMATS).day()) schedule.wday = moment(currentDateString, DATE_FORMATS).day();
    if (schedule.showpic !== epg.pic) schedule.showpic = epg.pic;
    if (schedule.showCustomPicForRecords !== epg.showCustomPicForRecords) schedule.showCustomPicForRecords = epg.showCustomPicForRecords || false;
    if (isNameChanged || isNewSchedule) schedule.year = parseInt(ScheduleClass.getYear(schedule)) || null;
    if (!schedule.createdForTtlIndex) schedule.createdForTtlIndex = new Date();

    schedules.push(schedule);
  });

  return { schedules, updateMode };
};

const updateOneSchedule = async (channel, rdatetime, unsetParams) => {
  await Schedule.collection.updateOne(
    {
      rdatetime,
      channel,
    },
    { $unset: { ...unsetParams } },
  );
};

const prepareUnsetParams = (shouldSkipEn = false) => {
  const unsetParams = { translation_quality: 1 };
  i18n.getLocales().forEach((locale) => {
    if (locale === 'he') return;
    if (shouldSkipEn && locale === 'en') return;

    unsetParams[`name_${locale}`] = 1;
    unsetParams[`description_${locale}`] = 1;
    unsetParams[`genre_${locale}`] = 1;
  });

  return unsetParams;
};

const updateSchedules = async (oldSchedules, unsetParams) => {
  if (!oldSchedules.some((schedule) => schedule.isModified())) return `attempted to update ${oldSchedules.length} schedules, but no changes detected`;

  for (let i = 0; i < oldSchedules.length; ++i) {
    const schedule = oldSchedules[i];

    if (schedule.isModified()) {
      await schedule.save();

      // remove non hebrew fields, they will be translate later
      if (unsetParams && Object.keys.length) await updateOneSchedule(schedule.channel, schedule.rdatetime, unsetParams);
    }
  }

  return `updated ${oldSchedules.length} schedules with fresh properties`;
};

const insertSchedules = async (schedules, oldSchedules, unsetParams, maxTime, date, channel, channelId) => {
  schedules.forEach((schedule, index) => {
    if (!schedule.toObject) return;

    schedules[index] = schedule.toObject();
    delete schedules[index]._id;
  });
  schedules.sort((a, b) => a.rdatetime - b.rdatetime);
  let previousSchedule = null;
  log.info('calculating lengths for shows');
  schedules.forEach((schedule) => {
    if (previousSchedule !== null) previousSchedule.lengthtime = schedule.rdatetime - previousSchedule.rdatetime;

    previousSchedule = schedule;
  });
  previousSchedule.lengthtime = maxTime - previousSchedule.rdatetime;
  log.info(`saving new schedules for ${date} on channel #${channelId}`);
  try {
    for (let i = 0; i < oldSchedules.length; ++i) {
      await oldSchedules[i].remove();
    }
    for (let i = 0; i < schedules.length; ++i) {
      const schedule = schedules[i];
      schedule.createdForTtlIndex = new Date();
      await Schedule.findOneAndUpdate({ rdatetime: schedule.rdatetime, channel: channel.id }, { ...schedule }, { upsert: true });
    }
  } catch (e) {
    throw new CannotSaveSchedule('saving failed, nothing saved, check your data', e);
  }
  log.info(`inserted ${schedules.length} new schedules`);
  log.info(`clearing old schedules (${oldSchedules.length}) for ${date} on channel #${channelId}`);
  apiCache.withNamespace('schedules').flush('schedules', () => {});

  return `inserted ${schedules.length} new schedules`;
};

module.exports = async ({ channelId, date, source, epgs }) => {
  log.info(`trying to add fresh schedules for ${date} to channel #${channelId} from source '${source}'`);
  const channel = await Channel.findOne({ id: channelId }).exec();

  if (!channel) throw new ChannelNotFound(`no such channel #${channelId}`);
  if (!epgs || !Array.isArray(epgs)) throw new WrongEPGs('missing epgs or wrong type', epgs);

  const { currentUnixTime, tomorrowUnixTime, tomorrowDateString, currentDateString, minTime, maxTime } = prepareTimes(date);
  const query = {
    rdatetime: {
      $gte: minTime,
      $lt: maxTime,
    },
    channel: channel.id,
  };
  const oldSchedules = await Schedule.find(query).exec();
  const indexedOldSchedules = oldSchedules.reduce((index, schedule) => {
    index[schedule.rdatetime] = schedule;

    return index;
  }, {});
  const currentSchedulesCount = oldSchedules.length;

  if (currentSchedulesCount > epgs.length) {
    log.warn(
      `we have already ${currentSchedulesCount} schedules for ${date} on channel #${channelId}, ignoring request contains only ${epgs.length}`,
    );

    return 'ignored: already more detailed source used';
  }

  let updateMode = currentSchedulesCount === epgs.length;
  log.info(`converting epgs to schedules for ${date} to channel #${channelId}`);
  const convertScheduledsResult = convertEpgToSchedules(
    epgs,
    indexedOldSchedules,
    channel,
    updateMode,
    tomorrowDateString,
    currentDateString,
    tomorrowUnixTime,
    currentUnixTime,
  );
  const schedules = convertScheduledsResult.schedules;
  updateMode = convertScheduledsResult.updateMode;

  const unsetParams = prepareUnsetParams(!!schedules[0].name_en);

  if (updateMode) {
    return updateSchedules(oldSchedules, unsetParams);
  }

  return insertSchedules(schedules, oldSchedules, unsetParams, maxTime, date, channel, channelId);
};
