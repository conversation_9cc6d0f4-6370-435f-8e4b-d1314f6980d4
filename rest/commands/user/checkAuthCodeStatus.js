const config = require('../../../config');
const getCode = require('./getAndValidateAuthCode');
const getUserGroupForWebcamApp = require('../../helpers/getUserGroupForWebcamApp');
const User = require('../../model/audb/User');
const Session = require('../../model/audb/Session');

const regenerateSession = async (req, user) => new Promise((resolve, reject) => {
  req.session.regenerate((err) => {
    if (err) {
      // eslint-disable-next-line prefer-promise-reject-errors
      return reject(null);
    }

    req.session.passport = { user: user.id };
    req.session.user_id = user.id;

    req.session.save(() => {
      resolve(req.sessionID);
    });
  });
});

module.exports = async (req, code, locale = config.i18n.defaultLocale) => {
  const authCode = await getCode(code, locale);

  const response = {
    errorcode: 0,
    code: authCode.code.toUpperCase(),
    type: authCode.type,
  };

  if (authCode.dName) response.dName = authCode.dName;
  if (authCode.os) response.os = authCode.os;
  if (authCode.sid) {
    const userSession = await Session.findOne({ __sid: authCode.sid }).lean().cache(3600);

    if (userSession) {
      const user = await User.findOne({ id: userSession.user_id }).lean().cache(3600);

      if (user) {
        response.webcug = await getUserGroupForWebcamApp(user, authCode.appName);

        if (!authCode.sidUpdated) {
          authCode.uid = user.id;
          authCode.sid = await regenerateSession(req, user);

          if (authCode.sid) authCode.sidUpdated = true;

          await authCode.save();
        }
      }
    }

    response.sid = authCode.sid;
  }

  return response;
};
