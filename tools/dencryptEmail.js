// Standalone email decryption tool using built-in crypto module
const crypto = require('crypto');

// Constants from User.js
const encryptionKey = '#%#SD23!';

/**
 * Legacy mcrypt-style decryption (for backward compatibility)
 */
function decryptEmailLegacy(em) {
  if (!em) return '';

  try {
    const MCrypt = require('mcrypt').MCrypt;

    const desEcb = new MCrypt('des', 'ecb');
    desEcb.open(encryptionKey);
    const plaintext = desEcb.decrypt(Buffer.from(em, 'base64'));
    const serialized = plaintext.toString();
    const regex = /:"(.*?)"/gm;
    const match = regex.exec(serialized);

    return match ? match[1] : '';
  } catch (error) {
    return '';
  }
}

/**
 * Modern built-in crypto based decryption
 */
function decryptEmailModern(em) {
  if (!em) return '';

  try {
    const decipher = crypto.createDecipheriv('des-ecb', encryption<PERSON>ey, null);
    decipher.setAutoPadding(false);

    let decrypted = decipher.update(em, 'base64', 'binary');
    decrypted += decipher.final('binary');

    // Remove padding manually
    const lastByte = decrypted.charCodeAt(decrypted.length - 1);
    if (lastByte > 0 && lastByte <= 8) {
      // Check if it's valid PKCS padding
      let validPadding = true;
      for (let i = decrypted.length - lastByte; i < decrypted.length; i++) {
        if (decrypted.charCodeAt(i) !== lastByte) {
          validPadding = false;
          break;
        }
      }
      if (validPadding) {
        decrypted = decrypted.substring(0, decrypted.length - lastByte);
      }
    }

    // Parse serialized string format: s:length:"email";
    const regex = /:"(.*?)"/gm;
    const match = regex.exec(decrypted);

    return match ? match[1] : '';
  } catch (error) {
    return '';
  }
}

/**
 * Hybrid decryption: try legacy first, then modern
 */
function decryptEmailHybrid(em) {
  if (!em) return '';

  // First try legacy mcrypt method
  let decryptedEmail = decryptEmailLegacy(em);

  // If legacy method failed, try modern crypto method
  if (!decryptedEmail) {
    decryptedEmail = decryptEmailModern(em);
  }

  return decryptedEmail;
}

const myArgs = process.argv.slice(2);

console.log('🔓 Email Decryption Tool (Updated for Node.js 20.x.x compatibility)');
console.log('Using hybrid decryption: tries legacy mcrypt first, then built-in crypto\n');
console.log('Note: Run with NODE_OPTIONS="--openssl-legacy-provider" for DES support\n');

if (myArgs.length === 0) {
  console.log('Usage: node tools/dencryptEmail.js <encrypted_email1> [encrypted_email2] ...');
  console.log('Example: node tools/dencryptEmail.js "base64_encrypted_string"');
  process.exit(1);
}

for (let i = 0; i < myArgs.length; i++) {
  const encryptedEmail = myArgs[i];
  console.log(`🔒 Encrypted: ${encryptedEmail}`);

  try {
    // Use the hybrid decryption method
    const decrypted = decryptEmailHybrid(encryptedEmail);

    if (decrypted) {
      console.log(`📧 Decrypted: ${decrypted}`);
      console.log(`✅ Status: SUCCESS`);

      // Test which method was used
      const legacyResult = decryptEmailLegacy(encryptedEmail);
      const modernResult = decryptEmailModern(encryptedEmail);

      if (legacyResult) {
        console.log(`🔧 Method: Legacy mcrypt`);
      } else if (modernResult) {
        console.log(`🔧 Method: Modern built-in crypto`);
      }

    } else {
      console.log(`❌ Decrypted: FAILED`);
      console.log(`❌ Status: Could not decrypt with any method`);
    }

  } catch (error) {
    console.log(`❌ Error decrypting: ${error.message}`);
    console.log(`❌ Status: ERROR`);
  }

  console.log(''); // Empty line for readability
}
