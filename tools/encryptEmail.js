// Standalone email encryption tool using built-in crypto module
const crypto = require('crypto');

// Constants from User.js
const encryptionKey = '#%#SD23!';
const serializeString = (str) => `s:${str.length}:"${str}";`;

/**
 * Encrypt email using built-in crypto module (same as User.encryptEmail)
 */
function encryptEmail(email) {
  if (!email) return '';

  try {
    let serialized = serializeString(email.toLowerCase());

    // Apply PKCS padding manually (DES block size is 8 bytes)
    const blockSize = 8;
    const pad = blockSize - (serialized.length % blockSize);
    if (pad < blockSize) {
      serialized += new Array(pad + 1).join(String.fromCharCode(pad));
    }

    const cipher = crypto.createCipheriv('des-ecb', encryptionKey, null);
    cipher.setAutoPadding(false); // We handle padding manually

    let encrypted = cipher.update(serialized, 'binary', 'base64');
    encrypted += cipher.final('base64');

    return encrypted;
  } catch (error) {
    console.error('Error encrypting email:', error);
    return '';
  }
}

/**
 * Decrypt email using built-in crypto module (same as User.decryptEmailModern)
 */
function decryptEmail(em) {
  if (!em) return '';

  try {
    const decipher = crypto.createDecipheriv('des-ecb', encryptionKey, null);
    decipher.setAutoPadding(false);

    let decrypted = decipher.update(em, 'base64', 'binary');
    decrypted += decipher.final('binary');

    // Remove padding manually
    const lastByte = decrypted.charCodeAt(decrypted.length - 1);
    if (lastByte > 0 && lastByte <= 8) {
      // Check if it's valid PKCS padding
      let validPadding = true;
      for (let i = decrypted.length - lastByte; i < decrypted.length; i++) {
        if (decrypted.charCodeAt(i) !== lastByte) {
          validPadding = false;
          break;
        }
      }
      if (validPadding) {
        decrypted = decrypted.substring(0, decrypted.length - lastByte);
      }
    }

    // Parse serialized string format: s:length:"email";
    const regex = /:"(.*?)"/gm;
    const match = regex.exec(decrypted);

    return match ? match[1] : '';
  } catch (error) {
    return '';
  }
}

const myArgs = process.argv.slice(2);

console.log('🔐 Email Encryption Tool (Updated for Node.js 20.x.x compatibility)');
console.log('Using built-in crypto module with --openssl-legacy-provider support\n');

if (myArgs.length === 0) {
  console.log('Usage: node tools/encryptEmail.js <email1> [email2] [email3] ...');
  console.log('Example: node tools/encryptEmail.js <EMAIL> <EMAIL>');
  console.log('Note: Run with NODE_OPTIONS="--openssl-legacy-provider" for DES support');
  process.exit(1);
}

for (let i = 0; i < myArgs.length; i++) {
  const email = myArgs[i];
  console.log(`📧 Email: ${email}`);

  try {
    // Use the new built-in crypto encryption method
    const encrypted = encryptEmail(email);
    console.log(`🔒 Encrypted: ${encrypted}`);

    // Test decryption to verify it works
    const decrypted = decryptEmail(encrypted);
    const isValid = decrypted === email;

    console.log(`🔓 Decrypted: ${decrypted}`);
    console.log(`✅ Valid: ${isValid ? 'YES' : 'NO'}`);

    if (!isValid) {
      console.log(`❌ ERROR: Decryption failed!`);
      console.log(`   Expected: ${email}`);
      console.log(`   Got: ${decrypted}`);
    }

  } catch (error) {
    console.log(`❌ Error encrypting ${email}: ${error.message}`);
  }

  console.log(''); // Empty line for readability
}
